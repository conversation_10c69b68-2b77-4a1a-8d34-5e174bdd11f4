<?php
/**
 * <PERSON>amlanan Siparişler Test
 * Bu dosya sadece tamamlanan siparişlerin dahil edildi<PERSON>ini test eder
 */

// WordPress yüklenmesini bekle
if (!defined('ABSPATH')) {
    exit('WordPress yüklenmedi!');
}

/**
 * Tamamlanan siparişler testini çalıştır
 */
function test_completed_orders_only() {
    echo "<h2>Tamamlanan Siparişler Test</h2>";
    
    // Sadece admin kullanıcılar için
    if (!current_user_can('manage_options')) {
        echo "<p style='color: red;'>Bu sayfaya erişim yetkiniz yok.</p>";
        return;
    }
    
    global $wpdb;
    
    // Mevcut kullanıcının ürünlerini al
    $current_user_id = get_current_user_id();
    $user_products = get_posts([
        'post_type' => 'product',
        'post_status' => 'any',
        'author' => $current_user_id,
        'posts_per_page' => -1,
        'fields' => 'ids'
    ]);
    
    if (empty($user_products)) {
        echo "<p style='color: orange;'>⚠️ Kullanıcının hiç ürünü yok</p>";
        return;
    }
    
    echo "<p>Kullanıcı ID: " . $current_user_id . "</p>";
    echo "<p>Ürün sayısı: " . count($user_products) . "</p>";
    
    // WooCommerce Analytics tablosu kontrolü
    $product_lookup_table = $wpdb->prefix . 'wc_order_product_lookup';
    $order_stats_table = $wpdb->prefix . 'wc_order_stats';
    
    $product_lookup_exists = $wpdb->get_var("SHOW TABLES LIKE '{$product_lookup_table}'");
    $order_stats_exists = $wpdb->get_var("SHOW TABLES LIKE '{$order_stats_table}'");
    
    if (!$product_lookup_exists || !$order_stats_exists) {
        echo "<p style='color: red;'>❌ WooCommerce Analytics tabloları mevcut değil</p>";
        return;
    }
    
    echo "<h3>Sipariş Durum Analizi</h3>";
    
    // Tüm sipariş durumları için ayrı ayrı hesapla
    $statuses = ['wc-completed', 'wc-processing', 'wc-on-hold', 'wc-pending', 'wc-cancelled', 'wc-refunded', 'wc-failed'];
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Sipariş Durumu</th><th>Sipariş Sayısı</th><th>Toplam Gelir</th><th>Dahil Edildi Mi?</th></tr>";
    
    $total_included_revenue = 0;
    $total_excluded_revenue = 0;
    
    foreach ($statuses as $status) {
        $result = $wpdb->get_row($wpdb->prepare("
            SELECT 
                COUNT(DISTINCT pl.order_id) as order_count,
                SUM(pl.product_net_revenue) as total_revenue
            FROM {$product_lookup_table} pl
            INNER JOIN {$order_stats_table} os ON pl.order_id = os.order_id
            WHERE os.status = %s
                AND pl.product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
        ", $status));
        
        $order_count = $result ? $result->order_count : 0;
        $revenue = $result ? $result->total_revenue : 0;
        
        $is_included = ($status === 'wc-completed');
        $status_class = $is_included ? 'color: green; font-weight: bold;' : 'color: #666;';
        $included_text = $is_included ? '✅ EVET' : '❌ Hayır';
        
        if ($is_included) {
            $total_included_revenue += $revenue;
        } else {
            $total_excluded_revenue += $revenue;
        }
        
        echo "<tr style='{$status_class}'>";
        echo "<td>" . str_replace('wc-', '', $status) . "</td>";
        echo "<td>" . number_format($order_count) . "</td>";
        echo "<td>" . number_format($revenue, 2) . " " . get_woocommerce_currency_symbol() . "</td>";
        echo "<td>{$included_text}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>Özet</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Kategori</th><th>Gelir</th></tr>";
    echo "<tr style='color: green; font-weight: bold;'><td>Dahil Edilen (Tamamlanan)</td><td>" . number_format($total_included_revenue, 2) . " " . get_woocommerce_currency_symbol() . "</td></tr>";
    echo "<tr style='color: #666;'><td>Dahil Edilmeyen (Diğer Durumlar)</td><td>" . number_format($total_excluded_revenue, 2) . " " . get_woocommerce_currency_symbol() . "</td></tr>";
    echo "<tr style='font-weight: bold;'><td>Toplam (Tüm Durumlar)</td><td>" . number_format($total_included_revenue + $total_excluded_revenue, 2) . " " . get_woocommerce_currency_symbol() . "</td></tr>";
    echo "</table>";
    
    // Role Custom'ın hesapladığı değerle karşılaştır
    if (class_exists('Role_Custom')) {
        echo "<h3>Role Custom Karşılaştırması</h3>";
        
        $role_custom = Role_Custom::get_instance();
        $reflection = new ReflectionClass($role_custom);
        $method = $reflection->getMethod('get_user_stats');
        $method->setAccessible(true);
        $our_stats = $method->invoke($role_custom);
        
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Kaynak</th><th>Hesaplanan Gelir</th><th>Durum</th></tr>";
        echo "<tr><td><strong>Role Custom Reports</strong></td><td>" . number_format($our_stats['total_revenue'], 2) . " " . get_woocommerce_currency_symbol() . "</td>";
        
        if (abs($our_stats['total_revenue'] - $total_included_revenue) < 0.01) {
            echo "<td style='color: green;'>✅ Doğru (Sadece tamamlanan)</td>";
        } else {
            echo "<td style='color: red;'>❌ Hatalı</td>";
        }
        echo "</tr>";
        
        echo "<tr><td><strong>Tamamlanan Siparişler</strong></td><td>" . number_format($total_included_revenue, 2) . " " . get_woocommerce_currency_symbol() . "</td><td style='color: green;'>✅ Referans</td></tr>";
        echo "</table>";
        
        if (abs($our_stats['total_revenue'] - $total_included_revenue) < 0.01) {
            echo "<p style='color: green; font-weight: bold;'>✅ BAŞARILI: Role Custom sadece tamamlanan siparişleri hesaplıyor!</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ HATA: Role Custom yanlış hesaplama yapıyor!</p>";
            $diff = abs($our_stats['total_revenue'] - $total_included_revenue);
            echo "<p>Fark: " . number_format($diff, 2) . " " . get_woocommerce_currency_symbol() . "</p>";
        }
    }
    
    echo "<h3>Sonuç</h3>";
    echo "<ul>";
    echo "<li>✅ Sadece <strong>tamamlanan</strong> (<code>wc-completed</code>) siparişler gelir hesaplamasına dahil ediliyor</li>";
    echo "<li>❌ İşleme alınan (<code>wc-processing</code>) siparişler dahil edilmiyor</li>";
    echo "<li>❌ Beklemede (<code>wc-on-hold</code>) siparişler dahil edilmiyor</li>";
    echo "<li>❌ Diğer durumlar (pending, cancelled, refunded, failed) dahil edilmiyor</li>";
    echo "</ul>";
    
    if ($total_excluded_revenue > 0) {
        echo "<p style='color: orange;'><strong>Not:</strong> " . number_format($total_excluded_revenue, 2) . " " . get_woocommerce_currency_symbol() . " değerindeki diğer durumlardaki siparişler gelir hesaplamasına dahil edilmedi.</p>";
    }
}

// Admin panelinde test çalıştır
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        if (isset($_GET['test_completed_orders'])) {
            echo '<div class="notice notice-info is-dismissible" style="max-width: 1200px;">';
            test_completed_orders_only();
            echo '</div>';
        }
    });
    
    // Test linkini admin bar'a ekle
    add_action('admin_bar_menu', function($wp_admin_bar) {
        $wp_admin_bar->add_node([
            'id' => 'test-completed-orders',
            'title' => 'Test Completed Orders',
            'href' => admin_url('admin.php?test_completed_orders=1'),
            'meta' => ['title' => 'Test Completed Orders Only']
        ]);
    }, 100);
}
