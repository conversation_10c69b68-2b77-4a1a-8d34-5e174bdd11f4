<?php
/**
 * WooCommerce Analytics Debug Tool
 * Bu dosya WooCommerce Analytics tablolarını incelemek için kullanılır
 */

// WordPress yüklenmesini bekle
if (!defined('ABSPATH')) {
    exit('WordPress yüklenmedi!');
}

/**
 * WooCommerce Analytics tablolarını debug et
 */
function debug_woocommerce_analytics() {
    echo "<h2>WooCommerce Analytics Debug</h2>";
    
    // Sadece admin kullanıcılar için
    if (!current_user_can('manage_options')) {
        echo "<p style='color: red;'>Bu sayfaya erişim yetkiniz yok.</p>";
        return;
    }
    
    global $wpdb;
    
    // Mevcut kullanıcının ürünlerini al
    $current_user_id = get_current_user_id();
    $user_products = get_posts([
        'post_type' => 'product',
        'post_status' => 'any',
        'author' => $current_user_id,
        'posts_per_page' => 5, // İlk 5 ürün
        'fields' => 'ids'
    ]);
    
    echo "<h3>Kullanıcı Bilgileri</h3>";
    echo "<p>Kullanıcı ID: " . $current_user_id . "</p>";
    echo "<p>Ürün sayısı: " . count($user_products) . "</p>";
    if (!empty($user_products)) {
        echo "<p>İlk 5 ürün ID: " . implode(', ', $user_products) . "</p>";
    }
    
    // Tablo varlığını kontrol et
    $product_lookup_table = $wpdb->prefix . 'wc_order_product_lookup';
    $order_stats_table = $wpdb->prefix . 'wc_order_stats';
    
    echo "<h3>Tablo Varlık Kontrolü</h3>";
    
    $product_lookup_exists = $wpdb->get_var("SHOW TABLES LIKE '{$product_lookup_table}'");
    $order_stats_exists = $wpdb->get_var("SHOW TABLES LIKE '{$order_stats_table}'");
    
    if ($product_lookup_exists) {
        echo "<p style='color: green;'>✅ {$product_lookup_table} tablosu mevcut</p>";
        
        // Tablo yapısını göster
        $columns = $wpdb->get_results("DESCRIBE {$product_lookup_table}");
        echo "<h4>Tablo Yapısı:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Sütun</th><th>Tip</th><th>Null</th><th>Key</th></tr>";
        foreach ($columns as $column) {
            echo "<tr><td>{$column->Field}</td><td>{$column->Type}</td><td>{$column->Null}</td><td>{$column->Key}</td></tr>";
        }
        echo "</table>";
        
        // Toplam kayıt sayısı
        $total_records = $wpdb->get_var("SELECT COUNT(*) FROM {$product_lookup_table}");
        echo "<p>Toplam kayıt sayısı: " . number_format($total_records) . "</p>";
        
        if (!empty($user_products)) {
            // Kullanıcının ürünleri için kayıtlar
            $user_records = $wpdb->get_var("
                SELECT COUNT(*) 
                FROM {$product_lookup_table} 
                WHERE product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
            ");
            echo "<p>Kullanıcının ürünleri için kayıt sayısı: " . number_format($user_records) . "</p>";
            
            if ($user_records > 0) {
                // Örnek kayıtları göster
                $sample_records = $wpdb->get_results("
                    SELECT * 
                    FROM {$product_lookup_table} 
                    WHERE product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
                    ORDER BY date_created DESC
                    LIMIT 5
                ");
                
                echo "<h4>Örnek Kayıtlar (Son 5):</h4>";
                if (!empty($sample_records)) {
                    echo "<table border='1' cellpadding='5' cellspacing='0' style='font-size: 12px;'>";
                    echo "<tr><th>Order ID</th><th>Product ID</th><th>Qty</th><th>Net Revenue</th><th>Date</th></tr>";
                    foreach ($sample_records as $record) {
                        echo "<tr>";
                        echo "<td>{$record->order_id}</td>";
                        echo "<td>{$record->product_id}</td>";
                        echo "<td>{$record->product_qty}</td>";
                        echo "<td>" . number_format($record->product_net_revenue, 2) . "</td>";
                        echo "<td>{$record->date_created}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        }
    } else {
        echo "<p style='color: red;'>❌ {$product_lookup_table} tablosu mevcut değil</p>";
    }
    
    if ($order_stats_exists) {
        echo "<p style='color: green;'>✅ {$order_stats_table} tablosu mevcut</p>";
        
        // Toplam kayıt sayısı
        $total_orders = $wpdb->get_var("SELECT COUNT(*) FROM {$order_stats_table}");
        echo "<p>Toplam sipariş sayısı: " . number_format($total_orders) . "</p>";
        
        // Durum dağılımı
        $status_distribution = $wpdb->get_results("
            SELECT status, COUNT(*) as count 
            FROM {$order_stats_table} 
            GROUP BY status 
            ORDER BY count DESC
        ");
        
        echo "<h4>Sipariş Durum Dağılımı:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Durum</th><th>Sayı</th></tr>";
        foreach ($status_distribution as $status) {
            echo "<tr><td>{$status->status}</td><td>" . number_format($status->count) . "</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ {$order_stats_table} tablosu mevcut değil</p>";
    }
    
    // Geleneksel yöntemle karşılaştırma
    if (!empty($user_products)) {
        echo "<h3>Gelir Hesaplama Karşılaştırması</h3>";
        
        // Analytics yöntemi
        if ($product_lookup_exists && $order_stats_exists) {
            $analytics_result = $wpdb->get_row("
                SELECT 
                    COUNT(DISTINCT pl.order_id) as total_orders,
                    SUM(pl.product_net_revenue) as total_revenue,
                    SUM(pl.product_qty) as total_qty
                FROM {$product_lookup_table} pl
                INNER JOIN {$order_stats_table} os ON pl.order_id = os.order_id
                WHERE os.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                    AND pl.product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
            ");
        }
        
        // Geleneksel yöntem
        $traditional_result = $wpdb->get_row("
            SELECT 
                COUNT(DISTINCT p.ID) as total_orders,
                SUM(CAST(oim_total.meta_value AS DECIMAL(10,2))) as total_revenue,
                SUM(CAST(oim_qty.meta_value AS SIGNED)) as total_qty
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
            INNER JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_product ON oi.order_item_id = oim_product.order_item_id 
                AND oim_product.meta_key = '_product_id'
            LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_total ON oi.order_item_id = oim_total.order_item_id 
                AND oim_total.meta_key = '_line_total'
            LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta oim_qty ON oi.order_item_id = oim_qty.order_item_id 
                AND oim_qty.meta_key = '_qty'
            WHERE p.post_type = 'shop_order'
                AND p.post_status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                AND CAST(oim_product.meta_value AS UNSIGNED) IN (" . implode(',', array_map('intval', $user_products)) . ")
        ");
        
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Yöntem</th><th>Sipariş</th><th>Gelir</th><th>Adet</th></tr>";
        
        if (isset($analytics_result)) {
            echo "<tr><td><strong>Analytics</strong></td><td>" . ($analytics_result->total_orders ?: 0) . "</td><td>" . number_format($analytics_result->total_revenue ?: 0, 2) . "</td><td>" . ($analytics_result->total_qty ?: 0) . "</td></tr>";
        }
        
        echo "<tr><td><strong>Geleneksel</strong></td><td>" . ($traditional_result->total_orders ?: 0) . "</td><td>" . number_format($traditional_result->total_revenue ?: 0, 2) . "</td><td>" . ($traditional_result->total_qty ?: 0) . "</td></tr>";
        echo "</table>";
        
        if (isset($analytics_result) && $analytics_result->total_revenue && $traditional_result->total_revenue) {
            $diff = abs($analytics_result->total_revenue - $traditional_result->total_revenue);
            echo "<p>Gelir farkı: " . number_format($diff, 2) . " " . get_woocommerce_currency_symbol() . "</p>";
            
            if ($diff < 0.01) {
                echo "<p style='color: green;'>✅ İki yöntem de aynı sonucu veriyor</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Yöntemler arasında fark var</p>";
            }
        }
    }
    
    echo "<h3>Öneriler</h3>";
    echo "<ul>";
    if (!$product_lookup_exists) {
        echo "<li>WooCommerce Analytics tablolarını oluşturmak için WooCommerce > Durum > Araçlar > 'Analytics verilerini yeniden oluştur' seçeneğini kullanın</li>";
    }
    echo "<li>Analytics tabloları varsa daha doğru gelir hesaplaması için <code>product_net_revenue</code> kullanılır</li>";
    echo "<li><strong>Tüm aktif siparişler</strong> (<code>wc-completed, wc-processing, wc-on-hold</code>) gelir hesaplamasına dahil edilir</li>";
    echo "<li>Bekleyen (<code>wc-pending</code>), iptal (<code>wc-cancelled</code>) ve başarısız (<code>wc-failed</code>) siparişler dahil edilmez</li>";
    echo "<li>Analytics tabloları yoksa fallback olarak geleneksel yöntem kullanılır</li>";
    echo "</ul>";
}

// Admin panelinde debug çalıştır
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        if (isset($_GET['debug_analytics'])) {
            echo '<div class="notice notice-info is-dismissible" style="max-width: 1200px;">';
            debug_woocommerce_analytics();
            echo '</div>';
        }
    });
    
    // Debug linkini admin bar'a ekle
    add_action('admin_bar_menu', function($wp_admin_bar) {
        $wp_admin_bar->add_node([
            'id' => 'debug-analytics',
            'title' => 'Debug Analytics',
            'href' => admin_url('admin.php?debug_analytics=1'),
            'meta' => ['title' => 'WooCommerce Analytics Debug']
        ]);
    }, 100);
}
