<?php
/**
 * Role Custom Reports Fixes Test
 * Bu dosya Reports sayfasındaki düzeltmeleri test eder
 */

// WordPress yüklenmesini bekle
if (!defined('ABSPATH')) {
    exit('WordPress yüklenmedi!');
}

/**
 * Reports düzeltmelerini test et
 */
function role_custom_test_reports_fixes() {
    echo "<h2>Role Custom Reports Düzeltmeleri Test</h2>";
    
    // Eklenti aktif mi?
    if (!class_exists('Role_Custom')) {
        echo "<p style='color: red;'>❌ Role Custom eklentisi aktif değil!</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ Role Custom eklentisi aktif</p>";
    
    // WooCommerce aktif mi?
    if (!class_exists('WooCommerce')) {
        echo "<p style='color: red;'>❌ WooCommerce eklentisi aktif değil!</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ WooCommerce eklentisi aktif</p>";

    // WooCommerce Analytics tabloları var mı?
    global $wpdb;
    $product_lookup_table = $wpdb->prefix . 'wc_order_product_lookup';
    $order_stats_table = $wpdb->prefix . 'wc_order_stats';

    $product_lookup_exists = $wpdb->get_var("SHOW TABLES LIKE '{$product_lookup_table}'");
    $order_stats_exists = $wpdb->get_var("SHOW TABLES LIKE '{$order_stats_table}'");

    if ($product_lookup_exists && $order_stats_exists) {
        echo "<p style='color: green;'>✅ WooCommerce Analytics tabloları mevcut</p>";
        echo "<p>- {$product_lookup_table} ✓</p>";
        echo "<p>- {$order_stats_table} ✓</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ WooCommerce Analytics tabloları eksik</p>";
        if (!$product_lookup_exists) echo "<p>- {$product_lookup_table} ❌</p>";
        if (!$order_stats_exists) echo "<p>- {$order_stats_table} ❌</p>";
        echo "<p>Fallback yöntem kullanılacak.</p>";
    }

    // Mevcut kullanıcı superole mu?
    $current_user = wp_get_current_user();
    if (!in_array('superole', $current_user->roles)) {
        echo "<p style='color: orange;'>⚠️ Test için bir kullanıcıyı superole rolüne atayın</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ Mevcut kullanıcı superole rolünde</p>";
    
    // Role Custom instance'ını al
    $role_custom = Role_Custom::get_instance();
    
    // Test 1: Kullanıcının ürünlerini al
    echo "<h3>Test 1: Kullanıcı Ürünleri</h3>";
    $reflection = new ReflectionClass($role_custom);
    $method = $reflection->getMethod('get_user_products');
    $method->setAccessible(true);
    $user_products = $method->invoke($role_custom);
    
    echo "<p>Kullanıcının ürün sayısı: " . count($user_products) . "</p>";
    if (count($user_products) > 0) {
        echo "<p style='color: green;'>✅ Kullanıcının ürünleri bulundu</p>";
        echo "<p>Ürün ID'leri: " . implode(', ', array_slice($user_products, 0, 5)) . (count($user_products) > 5 ? '...' : '') . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Kullanıcının hiç ürünü yok</p>";
    }
    
    // Test 2: Genel istatistikleri al
    echo "<h3>Test 2: Genel İstatistikler</h3>";
    $stats_method = $reflection->getMethod('get_user_stats');
    $stats_method->setAccessible(true);
    $stats = $stats_method->invoke($role_custom);
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>İstatistik</th><th>Değer</th></tr>";
    echo "<tr><td>Toplam Ürün</td><td>" . $stats['total_products'] . "</td></tr>";
    echo "<tr><td>Toplam Sipariş</td><td>" . $stats['total_orders'] . "</td></tr>";
    echo "<tr><td>Toplam Gelir</td><td>" . number_format($stats['total_revenue'], 2) . " " . get_woocommerce_currency_symbol() . "</td></tr>";
    echo "<tr><td>Ortalama Puan</td><td>" . $stats['avg_rating'] . "/5</td></tr>";
    echo "</table>";
    
    if ($stats['total_revenue'] > 0) {
        echo "<p style='color: green;'>✅ Gelir hesaplaması çalışıyor</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Henüz gelir verisi yok veya hesaplama sorunu var</p>";
    }
    
    // Test 3: Sipariş verilerini al (son 30 gün)
    echo "<h3>Test 3: Sipariş Verileri (Son 30 Gün)</h3>";
    $orders_method = $reflection->getMethod('get_user_orders_data');
    $orders_method->setAccessible(true);
    $orders_data = $orders_method->invoke($role_custom, 30);
    
    echo "<p>Son 30 günde " . count($orders_data) . " gün verisi alındı</p>";
    
    $total_orders = array_sum(array_column($orders_data, 'orders'));
    $total_revenue = array_sum(array_column($orders_data, 'revenue'));
    
    echo "<p>Toplam sipariş: " . $total_orders . "</p>";
    echo "<p>Toplam gelir: " . number_format($total_revenue, 2) . " " . get_woocommerce_currency_symbol() . "</p>";
    
    if ($total_revenue > 0) {
        echo "<p style='color: green;'>✅ Sipariş verisi hesaplaması çalışıyor</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Son 30 günde sipariş verisi yok</p>";
    }
    
    // Test 4: Özel tarih aralığı fonksiyonu
    echo "<h3>Test 4: Özel Tarih Aralığı Fonksiyonu</h3>";
    $range_method = $reflection->getMethod('get_user_orders_data_by_range');
    $range_method->setAccessible(true);
    
    $start_date = date('Y-m-d', strtotime('-7 days'));
    $end_date = date('Y-m-d');
    
    try {
        $range_data = $range_method->invoke($role_custom, $start_date, $end_date);
        echo "<p style='color: green;'>✅ Özel tarih aralığı fonksiyonu çalışıyor</p>";
        echo "<p>Tarih aralığı: " . $start_date . " - " . $end_date . "</p>";
        echo "<p>Veri sayısı: " . count($range_data) . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Özel tarih aralığı fonksiyonunda hata: " . $e->getMessage() . "</p>";
    }
    
    // Test 5: En çok satan ürünler
    echo "<h3>Test 5: En Çok Satan Ürünler</h3>";
    $products_method = $reflection->getMethod('get_top_selling_products');
    $products_method->setAccessible(true);
    $top_products = $products_method->invoke($role_custom, 5);
    
    if (!empty($top_products)) {
        echo "<p style='color: green;'>✅ En çok satan ürünler bulundu</p>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Ürün Adı</th><th>Satış Adedi</th><th>Gelir</th></tr>";
        foreach ($top_products as $product) {
            echo "<tr>";
            echo "<td>" . esc_html($product->product_name) . "</td>";
            echo "<td>" . $product->total_sold . "</td>";
            echo "<td>" . number_format($product->total_revenue, 2) . " " . get_woocommerce_currency_symbol() . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ En çok satan ürün verisi yok</p>";
    }
    
    // Test 6: Asset dosyaları
    echo "<h3>Test 6: Asset Dosyaları</h3>";
    
    $css_file = ROLE_CUSTOM_PLUGIN_DIR . 'assets/css/reports.css';
    $js_file = ROLE_CUSTOM_PLUGIN_DIR . 'assets/js/reports.js';
    
    if (file_exists($css_file)) {
        $css_size = filesize($css_file);
        echo "<p style='color: green;'>✅ CSS dosyası mevcut (" . number_format($css_size) . " bytes)</p>";
        
        // CSS'de özel tarih aralığı stillerini kontrol et
        $css_content = file_get_contents($css_file);
        if (strpos($css_content, 'custom-date-range') !== false) {
            echo "<p style='color: green;'>✅ Özel tarih aralığı CSS stilleri mevcut</p>";
        } else {
            echo "<p style='color: red;'>❌ Özel tarih aralığı CSS stilleri eksik</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ CSS dosyası bulunamadı</p>";
    }
    
    if (file_exists($js_file)) {
        $js_size = filesize($js_file);
        echo "<p style='color: green;'>✅ JavaScript dosyası mevcut (" . number_format($js_size) . " bytes)</p>";
        
        // JS'de özel tarih aralığı fonksiyonlarını kontrol et
        $js_content = file_get_contents($js_file);
        if (strpos($js_content, 'loadSalesDataByRange') !== false && strpos($js_content, 'loadRevenueDataByRange') !== false) {
            echo "<p style='color: green;'>✅ Özel tarih aralığı JavaScript fonksiyonları mevcut</p>";
        } else {
            echo "<p style='color: red;'>❌ Özel tarih aralığı JavaScript fonksiyonları eksik</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ JavaScript dosyası bulunamadı</p>";
    }
    
    // Test 7: WooCommerce Analytics ile karşılaştırma
    echo "<h3>Test 7: WooCommerce Analytics Karşılaştırması</h3>";

    if ($product_lookup_exists && $order_stats_exists && count($user_products) > 0) {
        // Bizim hesaplamamız
        $our_stats = $stats_method->invoke($role_custom);

        // WooCommerce Analytics'ten direkt veri al
        $analytics_query = "
            SELECT
                COUNT(DISTINCT pl.order_id) as total_orders,
                SUM(pl.product_net_revenue) as total_revenue
            FROM {$product_lookup_table} pl
            INNER JOIN {$order_stats_table} os ON pl.order_id = os.order_id
            WHERE os.status IN ('wc-completed', 'wc-processing', 'wc-on-hold')
                AND pl.product_id IN (" . implode(',', array_map('intval', $user_products)) . ")
        ";

        $analytics_result = $wpdb->get_row($analytics_query);

        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Kaynak</th><th>Toplam Sipariş</th><th>Toplam Gelir</th></tr>";
        echo "<tr><td><strong>Bizim Hesaplama</strong></td><td>" . $our_stats['total_orders'] . "</td><td>" . number_format($our_stats['total_revenue'], 2) . " " . get_woocommerce_currency_symbol() . "</td></tr>";
        echo "<tr><td><strong>WooCommerce Analytics</strong></td><td>" . ($analytics_result ? $analytics_result->total_orders : 0) . "</td><td>" . number_format($analytics_result ? $analytics_result->total_revenue : 0, 2) . " " . get_woocommerce_currency_symbol() . "</td></tr>";
        echo "</table>";

        if ($analytics_result && abs($our_stats['total_revenue'] - $analytics_result->total_revenue) < 0.01) {
            echo "<p style='color: green;'>✅ Gelir hesaplaması WooCommerce Analytics ile eşleşiyor!</p>";
        } else {
            echo "<p style='color: red;'>❌ Gelir hesaplaması WooCommerce Analytics ile eşleşmiyor</p>";
            if ($analytics_result) {
                $diff = abs($our_stats['total_revenue'] - $analytics_result->total_revenue);
                echo "<p>Fark: " . number_format($diff, 2) . " " . get_woocommerce_currency_symbol() . "</p>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠️ WooCommerce Analytics tabloları mevcut değil veya ürün yok</p>";
    }

    echo "<h3>Test Tamamlandı</h3>";
    echo "<p><strong>Düzeltmeler:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Toplam gelir hesaplaması WooCommerce Analytics ile uyumlu hale getirildi</li>";
    echo "<li>✅ <code>product_net_revenue</code> alanı kullanılıyor (WooCommerce Analytics ile aynı)</li>";
    echo "<li>✅ <strong>Tüm aktif siparişler</strong> (<code>wc-completed, wc-processing, wc-on-hold</code>) dahil ediliyor</li>";
    echo "<li>✅ Özel tarih aralığı seçeneği eklendi</li>";
    echo "<li>✅ Loading animasyonu kaldırıldı</li>";
    echo "<li>✅ Responsive tasarım iyileştirildi</li>";
    echo "<li>✅ Fallback sistemi: Analytics tabloları yoksa geleneksel yöntem kullanılıyor</li>";
    echo "</ul>";

    echo "<p>Reports sayfasına erişmek için: <a href='" . admin_url('admin.php?page=role-custom-reports') . "'>Raporlar</a></p>";
    echo "<p>WooCommerce Analytics ile karşılaştırmak için: <a href='" . admin_url('admin.php?page=wc-admin&path=/analytics/products') . "'>WooCommerce Analytics</a></p>";
}

// Admin panelinde test çalıştır
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        if (isset($_GET['role_custom_test_fixes'])) {
            echo '<div class="notice notice-info is-dismissible" style="max-width: 1200px;">';
            role_custom_test_reports_fixes();
            echo '</div>';
        }
    });
    
    // Test linkini admin bar'a ekle
    add_action('admin_bar_menu', function($wp_admin_bar) {
        $wp_admin_bar->add_node([
            'id' => 'role-custom-test-fixes',
            'title' => 'Test Reports Fixes',
            'href' => admin_url('admin.php?role_custom_test_fixes=1'),
            'meta' => ['title' => 'Role Custom Reports Fixes Test']
        ]);
    }, 100);
}
